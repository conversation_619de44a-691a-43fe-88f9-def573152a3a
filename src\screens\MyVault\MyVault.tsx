import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { useAuth } from "../../hooks/useAuth";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { supabase } from "../../lib/supabase";
import { AddBlockModal } from "../../components/AddBlockModal";
import { EditBlockModal } from "../../components/EditBlockModal";
import { VaultSearch } from "../../components/VaultSearch";
import Fuse from 'fuse.js';
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  Edit3,
  Plus,
  Calendar,
  FileText,
  Upload,
  Camera,
  MoreVertical,
  Pin,
  Trash2
} from "lucide-react";

interface Topic {
  id: string;
  user_id: string;
  name: string;
  smart_tag: string;
  banner_gradient: string;
  custom_banner_image?: string;
  is_pinned: boolean;
  total_items: number;
  position: number;
  created_at: string;
  updated_at: string;
}

// Helper interface for display
interface TopicDisplay extends Topic {
  createdDate: string; // formatted date for display
}

export const MyVault = (): JSX.Element => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [vaultName, setVaultName] = useState("My Vault");
  const [isEditingName, setIsEditingName] = useState(false);
  const [bannerImage, setBannerImage] = useState<string | null>(null);
  const [userSettings, setUserSettings] = useState<any>(null);
  const [showBannerUpload, setShowBannerUpload] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);


  const [showAddBlockModal, setShowAddBlockModal] = useState(false);
  const [showEditBlockModal, setShowEditBlockModal] = useState(false);
  const [editingBlock, setEditingBlock] = useState<Topic | null>(null);
  const [bannerUploading, setBannerUploading] = useState(false);
  const [nameEditing, setNameEditing] = useState(false);
  const itemsPerPage = 9;
  const fileInputRef = useRef<HTMLInputElement>(null);

  const userDropdownRef = useRef<HTMLDivElement>(null);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);

  const sidebarItems = [
    { icon: HomeIcon, label: "home", path: "/home" },
    { icon: Archive, label: vaultName.toLowerCase(), path: "/my-vault" },
    { icon: Brain, label: "mind games", path: "/mind-games" },
    { icon: Map, label: "visual maps", path: "/visual-maps" },
    { icon: Trophy, label: "achievements", path: "/achievements" },
  ];

  // Topics data from database
  const [topics, setTopics] = useState<TopicDisplay[]>([]);
  const [loading, setLoading] = useState(true);

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilter, setSearchFilter] = useState<'all' | 'blocks' | 'content' | 'tag'>('all');
  const [searchTag, setSearchTag] = useState('');
  const [filteredTopics, setFilteredTopics] = useState<TopicDisplay[]>([]);
  const [allBlocks, setAllBlocks] = useState<any[]>([]);
  const [allContents, setAllContents] = useState<any[]>([]);

  // Sort state - load from localStorage for persistence
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'tag' | 'items'>(() => {
    const saved = localStorage.getItem('vaultSortBy');
    return (saved as 'name' | 'date' | 'tag' | 'items') || 'date';
  });
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(() => {
    const saved = localStorage.getItem('vaultSortOrder');
    return (saved as 'asc' | 'desc') || 'desc';
  });

  // Database functions
  const loadTopics = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('user_id', user.uid)
        .eq('depth', 0)
        .order('position', { ascending: true });

      if (error) {
        console.error('Error loading topics:', error);
        return;
      }

      const topicsWithDisplay: TopicDisplay[] = (data || []).map(topic => ({
        ...topic,
        createdDate: formatDate(topic.created_at)
      }));

      setTopics(topicsWithDisplay);
      setFilteredTopics(topicsWithDisplay); // Initialize filtered topics
    } catch (error) {
      console.error('Error loading topics:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load all blocks and contents for search
  const loadSearchData = async () => {
    if (!user) return;

    try {
      // Load all blocks
      const { data: blocksData, error: blocksError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('user_id', user.uid)
        .order('created_at', { ascending: false });

      if (blocksError) {
        console.error('Error loading blocks for search:', blocksError);
      } else {
        setAllBlocks(blocksData || []);
      }

      // Load all block contents
      const { data: contentsData, error: contentsError } = await supabase
        .from('block_contents')
        .select('*')
        .eq('user_id', user.uid)
        .order('created_at', { ascending: false });

      if (contentsError) {
        console.error('Error loading contents for search:', contentsError);
      } else {
        setAllContents(contentsData || []);
      }
    } catch (error) {
      console.error('Error loading search data:', error);
    }
  };

  // Handle search changes from VaultSearch component
  const handleSearchChange = (query: string, filter: 'all' | 'blocks' | 'content' | 'tag', tag: string) => {
    setSearchQuery(query);
    setSearchFilter(filter);
    setSearchTag(tag);
  };

  // Handle sort changes from VaultSearch component
  const handleSortChange = (newSortBy: 'name' | 'date' | 'tag' | 'items', newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  // Filter topics based on search criteria
  useEffect(() => {
    if (!searchQuery.trim() && searchFilter === 'all' && !searchTag) {
      // No search - show all topics
      setFilteredTopics(topics);
      return;
    }

    let filtered: TopicDisplay[] = [];

    if (searchFilter === 'tag' && searchTag) {
      // Filter by tag only
      filtered = topics.filter(topic => topic.smart_tag === searchTag);
    } else if (searchQuery.trim()) {
      // Use fuzzy search
      const fuse = new Fuse(topics, {
        keys: [
          { name: 'name', weight: 0.7 },
          { name: 'smart_tag', weight: 0.3 }
        ],
        threshold: 0.4,
        includeScore: true,
        minMatchCharLength: 2
      });

      const results = fuse.search(searchQuery);
      filtered = results.map(result => result.item);

      // If searching for content, also include blocks that have matching content
      if (searchFilter === 'all' || searchFilter === 'content') {
        const contentFuse = new Fuse(allContents, {
          keys: [
            { name: 'title', weight: 0.6 },
            { name: 'content', weight: 0.4 }
          ],
          threshold: 0.5,
          includeScore: true,
          minMatchCharLength: 2
        });

        const contentResults = contentFuse.search(searchQuery);
        const blockIdsWithMatchingContent = new Set(
          contentResults.map(result => result.item.block_id)
        );

        // Find parent blocks (depth 0) that contain these matching content blocks
        const parentBlocksWithContent = topics.filter(topic => {
          // Check if this topic or any of its descendants have matching content
          const descendantBlocks = allBlocks.filter(block =>
            block.id === topic.id ||
            (block.parent_id && isDescendantOf(block, topic.id, allBlocks))
          );

          return descendantBlocks.some(block => blockIdsWithMatchingContent.has(block.id));
        });

        // Merge with existing filtered results (avoid duplicates)
        const existingIds = new Set(filtered.map(t => t.id));
        parentBlocksWithContent.forEach(topic => {
          if (!existingIds.has(topic.id)) {
            filtered.push(topic);
          }
        });
      }
    } else {
      // No query but has filter - show all topics
      filtered = topics;
    }

    setFilteredTopics(filtered);
  }, [searchQuery, searchFilter, searchTag, topics, allBlocks, allContents]);

  // Helper function to check if a block is a descendant of a parent
  const isDescendantOf = (block: any, parentId: string, allBlocks: any[]): boolean => {
    let currentBlock = block;
    while (currentBlock.parent_id) {
      if (currentBlock.parent_id === parentId) {
        return true;
      }
      currentBlock = allBlocks.find(b => b.id === currentBlock.parent_id);
      if (!currentBlock) break;
    }
    return false;
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  };

  // Load user settings from users table
  const loadUserSettings = async () => {
    if (!user) return;

    console.log('Loading user settings for user:', user.uid);

    try {
      // Get user data from users table
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.uid)
        .single();

      console.log('User data query result:', data, error);

      if (error) {
        console.error('Error loading user data:', error);
        return;
      }

      if (data) {
        console.log('Found user data:', data);
        setUserSettings(data);
        setVaultName(data.vault_name || data.display_name || 'My Vault');
        setBannerImage(data.vault_banner_image || null);
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  // Save user settings to users table
  const saveUserSettings = async (updates: { vault_name?: string; vault_banner_image?: string | null }) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    console.log('Saving user settings to users table:', updates);
    console.log('User ID:', user.uid);

    try {
      // Update the users table with vault settings
      console.log('Updating users table with vault settings...');
      const { error: updateError } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.uid);

      console.log('Update error:', updateError);

      if (updateError) {
        console.error('Error updating user data:', updateError);
        throw new Error(`Failed to update settings: ${updateError.message || 'Unknown error'}`);
      }

      // After successful update, fetch the complete user record to ensure we have all fields
      console.log('Fetching complete user data after update...');
      const { data: completeUserData, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.uid)
        .single();

      console.log('Complete user data after update:', completeUserData);
      console.log('Fetch error:', fetchError);

      if (fetchError) {
        console.error('Error fetching updated user data:', fetchError);
        throw new Error(`Failed to fetch updated data: ${fetchError.message || 'Unknown error'}`);
      }

      // Update local state with the complete user data
      setUserSettings(completeUserData);
      setVaultName(completeUserData.vault_name || completeUserData.display_name || 'My Vault');
      setBannerImage(completeUserData.vault_banner_image || null);

      // Trigger a manual broadcast with the complete data
      window.dispatchEvent(new CustomEvent('vaultSettingsUpdated', {
        detail: {
          vault_name: completeUserData.vault_name,
          vault_banner_image: completeUserData.vault_banner_image
        }
      }));

      console.log('Successfully updated user settings');
      return completeUserData;
    } catch (error) {
      console.error('Error saving user settings:', error);
      throw error;
    }
  };

  // Load topics and settings when user changes
  useEffect(() => {
    if (user) {
      loadTopics();
      loadUserSettings();
      loadSearchData();
    }
  }, [user]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return;

    // Subscribe to users table changes for vault settings
    const userSettingsSubscription = supabase
      .channel(`users_changes_vault_${user.uid}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.uid}`
        },
        (payload) => {
          console.log('User data changed in MyVault:', payload);
          if (payload.new) {
            setUserSettings(payload.new);
            setVaultName(payload.new.vault_name || payload.new.display_name || 'My Vault');
            setBannerImage(payload.new.vault_banner_image || null);
          }
        }
      )
      .subscribe();

    // Subscribe to vault_blocks changes
    const vaultBlocksSubscription = supabase
      .channel('vault_blocks_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'vault_blocks',
          filter: `user_id=eq.${user.uid}`
        },
        (payload) => {
          console.log('Vault blocks changed:', payload);
          if (payload.eventType === 'INSERT' && payload.new) {
            const newTopic: TopicDisplay = {
              ...payload.new,
              createdDate: formatDate(payload.new.created_at)
            } as TopicDisplay;
            setTopics(prev => [newTopic, ...prev]);
          } else if (payload.eventType === 'UPDATE' && payload.new) {
            const updatedTopic: TopicDisplay = {
              ...payload.new,
              createdDate: formatDate(payload.new.created_at)
            } as TopicDisplay;
            setTopics(prev => prev.map(topic =>
              topic.id === payload.new.id ? updatedTopic : topic
            ));
          } else if (payload.eventType === 'DELETE' && payload.old) {
            setTopics(prev => prev.filter(topic => topic.id !== payload.old.id));
          }
        }
      )
      .subscribe();

    // Cleanup subscriptions
    return () => {
      userSettingsSubscription.unsubscribe();
      vaultBlocksSubscription.unsubscribe();
    };
  }, [user]);

  // Initialize with empty topics - will load from database
  useEffect(() => {
    if (!user && topics.length === 0) {
      // Show sample data only if no user is logged in
      const sampleTopics: TopicDisplay[] = [
        {
          id: "sample-1",
          user_id: "sample",
          name: "Ancient Civilizations",
          smart_tag: "History",
          createdDate: "2 hours ago",
          total_items: 12,
          banner_gradient: "from-red-300 to-white",
          custom_banner_image: undefined,
          is_pinned: true,
          position: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      setTopics(sampleTopics);
    }
  }, [user, topics.length]);

  // Sort filtered topics with pinned ones first, then by selected sort criteria
  const sortedTopics = [...filteredTopics].sort((a, b) => {
    // Always prioritize pinned items first
    if (a.is_pinned && !b.is_pinned) return -1;
    if (!a.is_pinned && b.is_pinned) return 1;

    // Then sort by selected criteria
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      case 'tag':
        comparison = (a.smart_tag || '').localeCompare(b.smart_tag || '');
        break;
      case 'items':
        comparison = a.total_items - b.total_items;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const totalPages = Math.ceil(sortedTopics.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTopics = sortedTopics.slice(startIndex, endIndex);

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      // Clear sort preferences from localStorage on logout
      localStorage.removeItem('vaultSortBy');
      localStorage.removeItem('vaultSortOrder');

      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setLogoutLoading(false);
    }
  };

  // Close dropdowns when clicking outside and debug user data
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false);
      }

      // Close ellipsis menus when clicking outside
      const target = event.target as Element;
      if (!target.closest('.ellipsis-menu') && !target.closest('.ellipsis-button')) {
        setOpenMenuId(null);
      }
    };

    // Debug user data
    if (user) {
      console.log('User data in MyVault:', {
        displayName: user.displayName,
        photoURL: user.photoURL,
        email: user.email,
        uid: user.uid
      });
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [user]);

  const handleBannerUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    console.log('Banner upload started:', file.name, file.size, file.type);

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('Please select a valid image file');
      alert('Please select a valid image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      console.error('File size must be less than 5MB');
      alert('File size must be less than 5MB');
      return;
    }

    setBannerUploading(true);

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const imageData = e.target?.result as string;
          console.log('Image data length:', imageData.length);
          console.log('Saving banner to database...');

          const result = await saveUserSettings({ vault_banner_image: imageData });
          console.log('Banner save result:', result);

          setShowBannerUpload(false);
          console.log('Banner uploaded successfully');
        } catch (error) {
          console.error('Error uploading banner:', error);
          alert('Error uploading banner: ' + (error instanceof Error ? error.message : 'Unknown error'));
        } finally {
          setBannerUploading(false);
        }
      };
      reader.onerror = () => {
        console.error('Error reading file');
        alert('Error reading file');
        setBannerUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error processing file:', error);
      alert('Error processing file: ' + (error instanceof Error ? error.message : 'Unknown error'));
      setBannerUploading(false);
    }
  };

  const handleNameEdit = () => {
    setIsEditingName(true);
  };

  const handleNameSave = async (newName: string) => {
    if (!newName.trim()) {
      setIsEditingName(false);
      return;
    }

    // Validate name length
    if (newName.trim().length > 50) {
      console.error('Vault name must be 50 characters or less');
      alert('Vault name must be 50 characters or less');
      return;
    }

    setNameEditing(true);

    try {
      console.log('Saving vault name:', newName.trim());
      await saveUserSettings({ vault_name: newName.trim() });
      setIsEditingName(false);
      console.log('Vault name updated successfully');
    } catch (error) {
      console.error('Error updating vault name:', error);
      alert('Error updating vault name: ' + (error instanceof Error ? error.message : 'Unknown error'));
    } finally {
      setNameEditing(false);
    }
  };

  const handleNameCancel = () => {
    setIsEditingName(false);
    // Reset vault name to the saved value
    if (userSettings) {
      setVaultName(userSettings.vault_name || 'My Vault');
    }
  };

  const createNewTopic = async (blockData: {
    name: string;
    smart_tag: string;
    banner_gradient: string;
    custom_banner_image?: string;
  }) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    const newTopicData = {
      user_id: user.uid,
      name: blockData.name,
      smart_tag: blockData.smart_tag,
      banner_gradient: blockData.banner_gradient,
      custom_banner_image: blockData.custom_banner_image || null,
      is_pinned: false,
      total_items: 0,
      position: topics.length,
      depth: 0
    };

    try {
      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newTopicData])
        .select()
        .single();

      if (error) {
        console.error('Error creating topic:', error);
        throw new Error(`Failed to create block: ${error.message}`);
      }

      const newTopicDisplay: TopicDisplay = {
        ...data,
        createdDate: formatDate(data.created_at)
      };

      setTopics([newTopicDisplay, ...topics]);
      setCurrentPage(1); // Reset to first page when adding new topic
      return newTopicDisplay;
    } catch (error) {
      console.error('Error creating topic:', error);
      throw error;
    }
  };

  const handleCreateBlock = () => {
    setShowAddBlockModal(true);
  };

  const handlePinTopic = async (topicId: string) => {
    if (!user) return;

    const topic = topics.find(t => t.id === topicId);
    if (!topic) return;

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .update({ is_pinned: !topic.is_pinned })
        .eq('id', topicId)
        .eq('user_id', user.uid);

      if (error) {
        console.error('Error updating pin status:', error);
        return;
      }

      setTopics(topics.map(t =>
        t.id === topicId
          ? { ...t, is_pinned: !t.is_pinned }
          : t
      ));
    } catch (error) {
      console.error('Error updating pin status:', error);
    }

    setOpenMenuId(null);
  };

  const handleEditTopic = (topic: Topic) => {
    setEditingBlock(topic);
    setShowEditBlockModal(true);
    setOpenMenuId(null);
  };

  const handleUpdateBlock = async (blockData: {
    id: string;
    name: string;
    smart_tag: string;
    banner_gradient?: string;
    custom_banner_image?: string | null;
  }) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .update({
          name: blockData.name,
          smart_tag: blockData.smart_tag,
          banner_gradient: blockData.banner_gradient,
          custom_banner_image: blockData.custom_banner_image
        })
        .eq('id', blockData.id)
        .eq('user_id', user.uid);

      if (error) {
        console.error('Error updating block:', error);
        throw new Error(`Failed to update block: ${error.message}`);
      }

      setTopics(topics.map(topic =>
        topic.id === blockData.id
          ? {
              ...topic,
              name: blockData.name,
              smart_tag: blockData.smart_tag,
              banner_gradient: blockData.banner_gradient || topic.banner_gradient,
              custom_banner_image: blockData.custom_banner_image || undefined
            }
          : topic
      ));

      setShowEditBlockModal(false);
      setEditingBlock(null);
      return true;
    } catch (error) {
      console.error('Error updating block:', error);
      throw error;
    }
  };

  const handleDeleteTopic = async (topicId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .delete()
        .eq('id', topicId)
        .eq('user_id', user.uid);

      if (error) {
        console.error('Error deleting topic:', error);
        return;
      }

      setTopics(topics.filter(topic => topic.id !== topicId));
      setOpenMenuId(null);

      // Adjust current page if needed
      const newTotalPages = Math.ceil((topics.length - 1) / itemsPerPage);
      if (currentPage > newTotalPages && newTotalPages > 0) {
        setCurrentPage(newTotalPages);
      }
    } catch (error) {
      console.error('Error deleting topic:', error);
    }
  };



  if (loading) {
    return <LoadingScreen message="Loading your vault..." />;
  }

  return (
    <>
      {logoutLoading && <LoadingScreen message="signing you out..." />}
      <div
        className="min-h-screen w-full font-sans relative"
        style={{
          backgroundImage: 'url(/background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        {/* Subtle overlay to tone down the background */}
        <div className="absolute inset-0 bg-[#2E0406]/40 backdrop-blur-[0.5px]" />

      {/* Top Navigation Bar */}
      <div className="w-full px-3 py-6 relative z-10">
        <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4 relative z-50 max-w-[90%] mx-auto">
          <div className="flex items-center justify-between">
            {/* Left: Hamburger/Close and Logo */}
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
              >
                {sidebarOpen ? (
                  <X className="w-4 h-4 text-[#5a4a3a]/80" />
                ) : (
                  <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                )}
              </Button>

              <div className="flex items-center gap-3">
                <img
                  src="/app logo.png"
                  alt="Logo"
                  className="w-9 h-9 rounded-xl object-cover shadow-sm"
                />
              </div>
            </div>

            {/* Right: Level, Notifications, User */}
            <div className="flex items-center gap-3">
              {/* Level Progress - More Compact */}
              <div className="hidden md:flex items-center gap-3 bg-white/50 backdrop-blur-sm rounded-2xl px-4 py-2 border border-[#d4c7b8]/30">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-poppins font-medium text-[#2E0406]/90">Lv.1</span>
                  <div className="w-16 h-1.5 bg-[#d4c7b8]/60 rounded-full overflow-hidden">
                    <div className="w-0 h-full bg-gradient-to-r from-[#8b7355] to-[#a68b5b] rounded-full transition-all duration-300" />
                  </div>
                  <span className="text-xs font-poppins text-[#8b7355]/70 font-medium">0/20</span>
                </div>
              </div>


              {/* Notifications */}
              <div className="relative" ref={notificationDropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
                  className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200 relative"
                >
                  <Bell className="w-4 h-4 text-[#5a4a3a]/80" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-[#8b2635] rounded-full"></div>
                </Button>
              </div>

              {/* User */}
              <div className="relative flex items-center gap-2" ref={userDropdownRef}>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                  className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200 p-0"
                >
                  {user?.photoURL ? (
                    <img
                      src={user.photoURL}
                      alt="Profile"
                      className="w-8 h-8 rounded-full object-cover"
                      onError={(e) => {
                        console.log('Profile image failed to load:', user.photoURL);
                        e.currentTarget.style.display = 'none';
                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                      }}
                    />
                  ) : null}
                  <User className={`w-5 h-5 text-[#5a4a3a]/80 ${user?.photoURL ? 'hidden' : ''}`} />
                </Button>
                <span
                  className="text-sm font-poppins text-[#2E0406]/90 cursor-pointer"
                  onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                >
                  {user?.displayName || 'user'}
                </span>

                {/* User Dropdown */}
                {userDropdownOpen && (
                  <div className="absolute right-0 top-12 w-48 bg-[#faf7f2]/70 backdrop-blur-xl border border-[#e0d7cc]/40 rounded-xl shadow-2xl p-2 z-[200] before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#faf7f2]/30 before:via-[#f0ebe3]/20 before:to-[#e8ddd0]/25 before:rounded-xl before:pointer-events-none">
                    <div className="space-y-1 relative z-10">
                      <div className="px-3 py-2 text-[#2E0406] font-poppins font-medium text-sm border-b border-[#e0d7cc]/40">
                        {user?.displayName || 'User'}
                      </div>
                      <div className="px-3 py-1 text-[#8b7355] font-poppins text-xs">
                        {user?.email}
                      </div>
                      <button
                        className="w-full text-left px-3 py-2 text-[#5a4a3a] font-poppins text-sm hover:bg-[#e0d7cc]/30 rounded-lg transition-colors"
                        onClick={() => {
                          setUserDropdownOpen(false);
                          // Handle profile click
                        }}
                      >
                        Profile Settings
                      </button>
                      <button
                        className="w-full text-left px-3 py-2 text-[#8b2635] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors"
                        onClick={() => {
                          setUserDropdownOpen(false);
                          handleLogout();
                        }}
                      >
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Main Content Area */}
      <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
        {/* Sidebar */}
        <div className={`transition-all duration-500 ease-in-out ${
          sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
        }`}>
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
            {/* Full Sidebar Content */}
            <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
            }`}>
              {/* Navigation Items */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                      item.path === '/my-vault'
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-r before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                    <span className="relative z-10">{item.label}</span>
                  </Button>
                ))}
              </div>

              {/* Logout Button */}
              <div className="absolute bottom-4 left-4 right-4">
                <Button
                  variant="ghost"
                  onClick={handleLogout}
                  disabled={logoutLoading}
                  className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                >
                  <LogOut className="w-4 h-4" />
                  <span>{logoutLoading ? "Signing out..." : "Logout"}</span>
                </Button>
              </div>
            </div>

            {/* Icon-Only Sidebar Content */}
            <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
              sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
            }`}>
              {/* Navigation Icons */}
              <div className="space-y-2 mb-8">
                {sidebarItems.map((item, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="icon"
                    onClick={() => handleNavigation(item.path)}
                    className={`w-10 h-10 rounded-xl transition-all duration-200 relative ${
                      item.path === '/my-vault'
                        ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg before:absolute before:inset-0 before:bg-gradient-to-br before:from-[#2E0406]/30 before:to-[#3d1a1c]/20 before:rounded-xl before:pointer-events-none'
                        : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406] hover:backdrop-blur-sm hover:border hover:border-[#d4c7b8]/30 hover:shadow-md'
                    }`}
                    title={item.label}
                  >
                    <item.icon className="w-4 h-4 relative z-10" />
                  </Button>
                ))}
              </div>

              {/* Logout Icon */}
              <div className="absolute bottom-3 left-3">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleLogout}
                  disabled={logoutLoading}
                  className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 transition-all duration-500 ease-in-out">
          <div className="bg-gradient-to-br from-[#f8f4ee] via-[#faf7f2] to-[#f5f0e8] h-full overflow-hidden">
            {/* Scrollable Content Container */}
            <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
              <div className="p-8">
                {/* Custom Banner Section */}
                <div className="relative h-32 overflow-hidden rounded-3xl mb-6 shadow-2xl">
                  {bannerImage ? (
                    <img
                      src={bannerImage}
                      alt="Vault Banner"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-[#8b2635] via-[#6d1f2c] to-[#2E0406]" />
                  )}

                  {/* Banner Upload Button */}
                  <button
                    onClick={() => setShowBannerUpload(true)}
                    className="absolute top-4 right-4 bg-white/20 backdrop-blur-md border border-white/30 rounded-xl p-3 hover:bg-white/30 transition-all duration-200 group"
                  >
                    <Camera className="w-5 h-5 text-white group-hover:scale-110 transition-transform" />
                  </button>

                  {/* Banner Upload Modal */}
                  {showBannerUpload && (
                    <div className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center rounded-3xl">
                      <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl p-6 max-w-sm mx-4">
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-poppins font-semibold text-[#2E0406]">Upload Banner</h3>
                          <button
                            onClick={() => setShowBannerUpload(false)}
                            className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 flex items-center justify-center transition-all"
                          >
                            <X className="w-4 h-4 text-[#8b2635]" />
                          </button>
                        </div>

                        <input
                          ref={fileInputRef}
                          type="file"
                          accept="image/*"
                          onChange={handleBannerUpload}
                          className="hidden"
                        />



                        <div className="space-y-3">
                          <Button
                            onClick={() => fileInputRef.current?.click()}
                            disabled={bannerUploading}
                            className="w-full bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            {bannerUploading ? (
                              <>
                                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                Uploading...
                              </>
                            ) : (
                              <>
                                <Upload className="w-4 h-4 mr-2" />
                                {bannerImage ? 'Change Image' : 'Choose Image'}
                              </>
                            )}
                          </Button>

                          {bannerImage && (
                            <Button
                              onClick={async () => {
                                setBannerUploading(true);
                                try {
                                  await saveUserSettings({ vault_banner_image: null });
                                  setShowBannerUpload(false);
                                } catch (error) {
                                  console.error('Error removing banner:', error);
                                } finally {
                                  setBannerUploading(false);
                                }
                              }}
                              disabled={bannerUploading}
                              variant="outline"
                              className="w-full border-[#8b2635] text-[#8b2635] hover:bg-[#8b2635] hover:text-white rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {bannerUploading ? (
                                <>
                                  <div className="w-4 h-4 mr-2 border-2 border-[#8b2635] border-t-transparent rounded-full animate-spin" />
                                  Removing...
                                </>
                              ) : (
                                <>
                                  <X className="w-4 h-4 mr-2" />
                                  Remove Banner
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                {/* Title Section with New Block Button */}
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center gap-4">
                    {isEditingName ? (
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          value={vaultName}
                          onChange={(e) => setVaultName(e.target.value)}
                          onBlur={() => handleNameSave(vaultName)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleNameSave(vaultName);
                            } else if (e.key === 'Escape') {
                              handleNameCancel();
                            }
                          }}
                          disabled={nameEditing}
                          className="text-4xl md:text-5xl font-cormorant font-bold text-[#2E0406] bg-transparent border-b-2 border-[#8b2635] focus:outline-none focus:border-[#6d1f2c] transition-colors disabled:opacity-50"
                          autoFocus
                        />
                        {nameEditing && (
                          <div className="w-6 h-6 border-2 border-[#8b2635] border-t-transparent rounded-full animate-spin" />
                        )}
                      </div>
                    ) : (
                      <>
                        <h1 className="text-4xl md:text-5xl font-cormorant font-bold text-[#2E0406]">
                          {vaultName}
                        </h1>
                        <button
                          onClick={handleNameEdit}
                          disabled={nameEditing}
                          className="w-10 h-10 bg-[#8b2635]/10 hover:bg-[#8b2635]/20 rounded-xl flex items-center justify-center transition-all duration-200 group disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Edit3 className="w-5 h-5 text-[#8b2635] group-hover:scale-110 transition-transform" />
                        </button>
                      </>
                    )}
                  </div>

                  {/* New Block Button */}
                  <Button
                    onClick={handleCreateBlock}
                    className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-6 font-poppins shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    New Block
                  </Button>
                </div>

                {/* Search Section */}
                <div className="mb-8">
                  <VaultSearch
                    onSearchChange={handleSearchChange}
                    onSortChange={handleSortChange}
                  />
                </div>

                {/* Topics Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                  {currentTopics.map((topic) => (
                    <div
                      key={topic.id}
                      data-topic-id={topic.id}
                      onClick={() => navigate(`/block/${topic.id}`)}
                      className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative"
                    >
                      {/* Topic Banner */}
                      <div className="h-32 relative overflow-hidden rounded-t-2xl">
                        {/* Custom image or gradient background */}
                        {topic.custom_banner_image ? (
                          <img
                            src={topic.custom_banner_image}
                            alt="Custom Banner"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className={`w-full h-full bg-gradient-to-r ${topic.banner_gradient}`} />
                        )}

                        {/* Overlay for better text readability */}
                        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-black/10 to-black/20" />

                        {/* Pin indicator */}
                        {topic.is_pinned && (
                          <div className="absolute top-3 left-4">
                            <div className="bg-[#8b2635] rounded-full p-1.5 shadow-md">
                              <Pin className="w-3 h-3 text-white" />
                            </div>
                          </div>
                        )}

                        <div className="absolute bottom-3 left-4">
                          <div className="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-1">
                            <span className="text-[#8b2635] font-poppins text-sm font-medium">
                              {topic.smart_tag}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Topic Content */}
                      <div className="p-5">
                        {/* Title Line with Ellipsis Menu */}
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="flex-1 font-cormorant font-bold text-[#2E0406] text-xl group-hover:text-[#8b2635] transition-colors">
                            {topic.name}
                          </h3>

                          {/* Ellipsis Menu */}
                          <div className="relative">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setOpenMenuId(openMenuId === topic.id ? null : topic.id);
                              }}
                              className="ellipsis-button w-8 h-8 rounded-lg hover:bg-[#8b2635]/10 flex items-center justify-center transition-all duration-200 group"
                            >
                              <MoreVertical className="w-4 h-4 text-[#8b2635] group-hover:scale-110 transition-transform" />
                            </button>

                            {/* Dropdown Menu */}
                            {openMenuId === topic.id && (
                              <div
                                className="ellipsis-menu absolute right-0 top-10 w-44 bg-white/90 backdrop-blur-md border border-white/30 rounded-xl shadow-2xl p-2 z-[9999]"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditTopic(topic);
                                  }}
                                  className="w-full text-left px-3 py-2 text-[#2E0406] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors flex items-center gap-2"
                                >
                                  <Edit3 className="w-4 h-4" />
                                  Edit Block
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleDeleteTopic(topic.id);
                                  }}
                                  className="w-full text-left px-3 py-2 text-[#8b2635] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors flex items-center gap-2"
                                >
                                  <Trash2 className="w-4 h-4" />
                                  Delete Block
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handlePinTopic(topic.id);
                                  }}
                                  className="w-full text-left px-3 py-2 text-[#2E0406] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors flex items-center gap-2"
                                >
                                  <Pin className="w-4 h-4" />
                                  {topic.is_pinned ? 'Unpin Block' : 'Pin Block'}
                                </button>

                              </div>
                            )}
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-[#6d1f2c] text-sm">
                            <Calendar className="w-4 h-4" />
                            <span className="font-poppins">{topic.createdDate}</span>
                          </div>

                          <div className="flex items-center gap-2 text-[#6d1f2c] text-sm">
                            <FileText className="w-4 h-4" />
                            <span className="font-poppins">{topic.total_items} items</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Pagination Controls */}
                {topics.length > itemsPerPage && (
                  <div className="flex items-center justify-center gap-2 mb-8">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <span className="text-lg">‹</span>
                    </Button>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant="ghost"
                        size="icon"
                        onClick={() => setCurrentPage(page)}
                        className={`w-10 h-10 rounded-xl font-poppins text-sm transition-all duration-200 ${
                          currentPage === page
                            ? 'bg-[#8b2635] text-white hover:bg-[#6d1f2c]'
                            : 'text-[#8b2635] hover:bg-[#8b2635]/10'
                        }`}
                      >
                        {page}
                      </Button>
                    ))}

                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                    >
                      <span className="text-lg">›</span>
                    </Button>
                  </div>
                )}

                {/* Empty State */}
                {topics.length === 0 && (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Plus className="w-12 h-12 text-[#8b7355]" />
                    </div>
                    <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">Start Your Knowledge Journey</h3>
                    <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                      Create your first knowledge block to begin organizing your learning materials.
                    </p>
                    <Button
                      onClick={handleCreateBlock}
                      className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-8 font-poppins"
                    >
                      <Plus className="w-5 h-5 mr-2" />
                      Create First Block
                    </Button>
                  </div>
                )}

                {/* No Search Results State */}
                {topics.length > 0 && filteredTopics.length === 0 && (searchQuery.trim() || searchFilter !== 'all' || searchTag) && (
                  <div className="text-center py-16">
                    <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                      <Search className="w-12 h-12 text-[#8b7355]" />
                    </div>
                    <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No Results Found</h3>
                    <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                      {searchQuery.trim()
                        ? `No blocks found matching "${searchQuery}". Try different keywords or check your spelling.`
                        : searchTag
                        ? `No blocks found with the tag "${searchTag}".`
                        : 'No blocks match your current filter criteria.'
                      }
                    </p>
                    <Button
                      onClick={() => {
                        setSearchQuery('');
                        setSearchFilter('all');
                        setSearchTag('');
                      }}
                      variant="outline"
                      className="border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 px-8 font-poppins"
                    >
                      Clear Search
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>



        {/* Add Block Modal */}
        <AddBlockModal
          isOpen={showAddBlockModal}
          onClose={() => setShowAddBlockModal(false)}
          onCreateBlock={createNewTopic}
        />

        {/* Edit Block Modal */}
        <EditBlockModal
          isOpen={showEditBlockModal}
          onClose={() => {
            setShowEditBlockModal(false);
            setEditingBlock(null);
          }}
          onUpdateBlock={handleUpdateBlock}
          block={editingBlock}
        />
      </div>
    </>
  );
};
