import React, { useState, useRef, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { useAuth } from "../../hooks/useAuth";
import { LoadingScreen } from "../../components/ui/loading-screen";
import { supabase } from "../../lib/supabase";
import {
  Home as HomeIcon,
  Archive,
  Brain,
  Map,
  Trophy,
  LogOut,
  Search,
  Bell,
  User,
  X,
  Menu,
  ChevronRight,
  Plus,
  FileText,
  Edit3,
  MoreVertical,
  Pin,
  Trash2,
  Folder,
  FolderOpen,
  ChevronDown,
  FolderPlus,
  Code,
  Image,
  Video,
  Music,
  File
} from "lucide-react";
import { AddFolderModal } from "../../components/AddFolderModal";
import { VaultSearch } from "../../components/VaultSearch/VaultSearch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TabsContent } from "../../components/ui/tabs";
import Fuse from 'fuse.js';

interface VaultBlock {
  id: string;
  user_id: string;
  parent_id?: string;
  name: string;
  smart_tag: string;
  banner_gradient: string;
  custom_banner_image?: string;
  is_pinned: boolean;
  total_items: number;
  position: number;
  depth: number;
  created_at: string;
  updated_at: string;
}

interface BlockContent {
  id: string;
  block_id: string;
  title: string;
  content: string;
  content_type: 'text' | 'image' | 'video' | 'audio' | 'file';
  position: number;
  created_at: string;
  updated_at: string;
}

export const BlockContent = (): JSX.Element => {
  const { blockId } = useParams<{ blockId: string }>();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [loading, setLoading] = useState(true);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [currentBlock, setCurrentBlock] = useState<VaultBlock | null>(null);
  const [breadcrumb, setBreadcrumb] = useState<VaultBlock[]>([]);
  const [subBlocks, setSubBlocks] = useState<VaultBlock[]>([]);
  const [blockContents, setBlockContents] = useState<BlockContent[]>([]);
  const [showAddBlockModal, setShowAddBlockModal] = useState(false);
  const [showAddContentModal, setShowAddContentModal] = useState(false);
  const [showAddFolderModal, setShowAddFolderModal] = useState(false);
  const [showCreateDropdown, setShowCreateDropdown] = useState(false);

  // Search and filtering state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilter, setSearchFilter] = useState<'all' | 'blocks' | 'content' | 'tag'>('all');
  const [searchTag, setSearchTag] = useState('');
  const [filteredSubBlocks, setFilteredSubBlocks] = useState<VaultBlock[]>([]);
  const [filteredBlockContents, setFilteredBlockContents] = useState<BlockContent[]>([]);

  // Sort state
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'tag' | 'items'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Draft notes state
  const [draftNotes, setDraftNotes] = useState<BlockContent[]>([]);

  // Subblock management state
  const [editingSubBlockId, setEditingSubBlockId] = useState<string | null>(null);
  const [editingSubBlockName, setEditingSubBlockName] = useState<string>('');
  const [openSubBlockMenuId, setOpenSubBlockMenuId] = useState<string | null>(null);

  // File drop state
  const [isDragOver, setIsDragOver] = useState(false);

  // Date formatting function
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    // For items over 24 hours old, show dd/mm/yy hh:mm format
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear().toString().slice(-2);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };

  // Get file type icon based on content type or title extension
  const getFileTypeIcon = (content: BlockContent) => {
    const title = content.title?.toLowerCase() || '';
    const contentType = content.content_type || 'text';

    // Check by content type first
    if (contentType === 'image') {
      return { icon: Image, color: 'text-green-500', bgColor: 'bg-green-100' };
    }
    if (contentType === 'video') {
      return { icon: Video, color: 'text-red-500', bgColor: 'bg-red-100' };
    }
    if (contentType === 'audio') {
      return { icon: Music, color: 'text-purple-500', bgColor: 'bg-purple-100' };
    }

    // Check by file extension
    if (title.includes('.')) {
      const extension = title.split('.').pop() || '';

      // Code files
      if (['js', 'jsx', 'ts', 'tsx', 'py', 'java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'html', 'css', 'scss', 'sass', 'less', 'json', 'xml', 'yaml', 'yml', 'sql', 'sh', 'bat', 'ps1'].includes(extension)) {
        return { icon: Code, color: 'text-blue-500', bgColor: 'bg-blue-100' };
      }

      // Document files
      if (['doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension)) {
        return { icon: FileText, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      }

      // PDF files
      if (['pdf'].includes(extension)) {
        return { icon: FileText, color: 'text-red-600', bgColor: 'bg-red-100' };
      }

      // Archive files
      if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'].includes(extension)) {
        return { icon: Archive, color: 'text-orange-500', bgColor: 'bg-orange-100' };
      }

      // Image files
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'ico'].includes(extension)) {
        return { icon: Image, color: 'text-green-500', bgColor: 'bg-green-100' };
      }

      // Video files
      if (['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv', 'webm', 'm4v'].includes(extension)) {
        return { icon: Video, color: 'text-red-500', bgColor: 'bg-red-100' };
      }

      // Audio files
      if (['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'].includes(extension)) {
        return { icon: Music, color: 'text-purple-500', bgColor: 'bg-purple-100' };
      }
    }

    // Default to text file
    return { icon: FileText, color: 'text-gray-500', bgColor: 'bg-gray-100' };
  };

  // File drop handlers
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    if (!user || !currentBlock) return;

    const files = Array.from(e.dataTransfer.files);

    for (const file of files) {
      try {
        // Convert file to base64
        const base64 = await new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.readAsDataURL(file);
        });

        // Determine content type
        let contentType = 'file';
        if (file.type.startsWith('image/')) contentType = 'image';
        else if (file.type.startsWith('video/')) contentType = 'video';
        else if (file.type.startsWith('audio/')) contentType = 'audio';

        // Create new block content
        const newContent = {
          block_id: currentBlock.id,
          user_id: user.uid,
          title: file.name,
          content: base64,
          content_type: contentType,
          position: blockContents.length
        };

        const { data, error } = await supabase
          .from('block_contents')
          .insert([newContent])
          .select()
          .single();

        if (error) {
          console.error('Error uploading file:', error);
          continue;
        }

        // Update local state immediately
        setBlockContents(prev => [...prev, data]);
        setFilteredBlockContents(prev => [...prev, data]);

      } catch (error) {
        console.error('Error processing file:', file.name, error);
      }
    }
  };

  // Handle subblock editing
  const handleEditSubBlock = (subBlock: VaultBlock) => {
    setEditingSubBlockId(subBlock.id);
    setEditingSubBlockName(subBlock.name);
    setOpenSubBlockMenuId(null);
  };

  const handleSaveSubBlockName = async (subBlockId: string) => {
    if (!editingSubBlockName.trim()) return;

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .update({ name: editingSubBlockName.trim() })
        .eq('id', subBlockId);

      if (error) {
        console.error('Error updating subblock name:', error);
        return;
      }

      // Update local state
      setSubBlocks(prev => prev.map(block =>
        block.id === subBlockId
          ? { ...block, name: editingSubBlockName.trim() }
          : block
      ));

      setEditingSubBlockId(null);
      setEditingSubBlockName('');
    } catch (error) {
      console.error('Error updating subblock name:', error);
    }
  };

  const handleCancelEdit = () => {
    setEditingSubBlockId(null);
    setEditingSubBlockName('');
  };

  const handleDeleteSubBlock = async (subBlockId: string) => {
    if (!window.confirm('Are you sure you want to delete this folder? This action cannot be undone.')) {
      return;
    }

    try {
      const { error } = await supabase
        .from('vault_blocks')
        .delete()
        .eq('id', subBlockId);

      if (error) {
        console.error('Error deleting subblock:', error);
        return;
      }

      // Update local state
      setSubBlocks(prev => prev.filter(block => block.id !== subBlockId));
      setOpenSubBlockMenuId(null);

      // Update parent's total_items count
      if (currentBlock) {
        await supabase
          .from('vault_blocks')
          .update({ total_items: subBlocks.length - 1 })
          .eq('id', currentBlock.id);
      }
    } catch (error) {
      console.error('Error deleting subblock:', error);
    }
  };

  // Tab state
  const [activeTab, setActiveTab] = useState<'subblocks' | 'files' | 'drafts'>('subblocks');
  const [newBlockName, setNewBlockName] = useState("");
  const [newContentTitle, setNewContentTitle] = useState("");
  const [newContentText, setNewContentText] = useState("");

  // User settings for vault name and banner
  const [vaultName, setVaultName] = useState("My Vault");
  const [bannerImage, setBannerImage] = useState<string | null>(null);
  const [userSettings, setUserSettings] = useState<any>(null);

  const userDropdownRef = useRef<HTMLDivElement>(null);
  const createDropdownRef = useRef<HTMLDivElement>(null);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);

  // Load block data
  useEffect(() => {
    if (user && blockId) {
      loadBlockData();
      loadUserSettings();
    }
  }, [user, blockId]);

  // Initialize filtered arrays when data loads
  useEffect(() => {
    setFilteredSubBlocks(subBlocks);
    setFilteredBlockContents(blockContents);
  }, [subBlocks, blockContents, draftNotes]);

  // Handle click outside for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (createDropdownRef.current && !createDropdownRef.current.contains(event.target as Node)) {
        setShowCreateDropdown(false);
      }
      if (openSubBlockMenuId) {
        setOpenSubBlockMenuId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openSubBlockMenuId]);

  // Load user settings from users table
  const loadUserSettings = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.uid)
        .single();

      if (error) {
        console.error('Error loading user data:', error);
        return;
      }

      if (data) {
        setUserSettings(data);
        setVaultName(data.vault_name || data.display_name || 'My Vault');
        setBannerImage(data.vault_banner_image || null);
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  };

  // Set up real-time subscription for user settings
  useEffect(() => {
    if (!user) return;

    const userSettingsSubscription = supabase
      .channel(`users_changes_block_${user.uid}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.uid}`
        },
        (payload) => {
          console.log('User data changed in BlockContent:', payload);
          if (payload.new) {
            setUserSettings(payload.new);
            setVaultName(payload.new.vault_name || payload.new.display_name || 'My Vault');
            setBannerImage(payload.new.vault_banner_image || null);
          }
        }
      )
      .subscribe();

    // Listen for manual vault settings updates
    const handleVaultSettingsUpdate = (event: CustomEvent) => {
      console.log('Manual vault settings update received in BlockContent:', event.detail);
      if (event.detail.vault_name !== undefined) {
        setVaultName(event.detail.vault_name || 'My Vault');
      }
      if (event.detail.vault_banner_image !== undefined) {
        setBannerImage(event.detail.vault_banner_image || null);
      }
    };

    window.addEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);

    return () => {
      userSettingsSubscription.unsubscribe();
      window.removeEventListener('vaultSettingsUpdated', handleVaultSettingsUpdate as EventListener);
    };
  }, [user]);

  const loadBlockData = async () => {
    if (!user || !blockId) return;

    try {
      setLoading(true);

      // Load current block
      const { data: blockData, error: blockError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', blockId)
        .eq('user_id', user.uid)
        .single();

      if (blockError) {
        console.error('Error loading block:', blockError);
        navigate('/my-vault');
        return;
      }

      setCurrentBlock(blockData);

      // Build breadcrumb
      await buildBreadcrumb(blockData);

      // Load sub-blocks
      const { data: subBlocksData, error: subBlocksError } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('parent_id', blockId)
        .eq('user_id', user.uid)
        .order('position', { ascending: true });

      if (subBlocksError) {
        console.error('Error loading sub-blocks:', subBlocksError);
      } else {
        setSubBlocks(subBlocksData || []);
      }

      // Load block contents (published and drafts)
      const { data: contentsData, error: contentsError } = await supabase
        .from('block_contents')
        .select('*')
        .eq('block_id', blockId)
        .order('created_at', { ascending: false });

      if (contentsError) {
        console.error('Error loading block contents:', contentsError);
        setBlockContents([]);
        setDraftNotes([]);
      } else {
        const allContents = contentsData || [];
        // Separate published content from drafts
        const publishedContent = allContents.filter(content => content.status !== 'draft');
        const drafts = allContents.filter(content => content.status === 'draft');

        setBlockContents(publishedContent);
        setDraftNotes(drafts);
      }

    } catch (error) {
      console.error('Error loading block data:', error);
    } finally {
      setLoading(false);
    }
  };

  const buildBreadcrumb = async (block: VaultBlock) => {
    const breadcrumbPath: VaultBlock[] = [];
    let currentBlock = block;

    // Build path from current block to root
    while (currentBlock.parent_id) {
      const { data: parentData, error } = await supabase
        .from('vault_blocks')
        .select('*')
        .eq('id', currentBlock.parent_id)
        .eq('user_id', user!.uid)
        .single();

      if (error || !parentData) break;
      
      breadcrumbPath.unshift(parentData);
      currentBlock = parentData;
    }

    setBreadcrumb(breadcrumbPath);
  };

  const createSubBlock = async () => {
    if (!user || !currentBlock || !newBlockName.trim()) return;

    try {
      const newBlockData = {
        user_id: user.uid,
        parent_id: currentBlock.id,
        name: newBlockName.trim(),
        smart_tag: currentBlock.smart_tag,
        banner_gradient: currentBlock.banner_gradient,
        is_pinned: false,
        total_items: 0,
        position: subBlocks.length,
        depth: currentBlock.depth + 1
      };

      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newBlockData])
        .select()
        .single();

      if (error) {
        console.error('Error creating sub-block:', error);
        return;
      }

      setSubBlocks([...subBlocks, data]);
      setNewBlockName("");
      setShowAddBlockModal(false);

      // Update parent's total_items count
      await supabase
        .from('vault_blocks')
        .update({ total_items: subBlocks.length + 1 })
        .eq('id', currentBlock.id);

    } catch (error) {
      console.error('Error creating sub-block:', error);
    }
  };

  const createFolder = async (folderData: { name: string; color: string }) => {
    if (!user || !currentBlock) return;

    try {
      const newFolderData = {
        user_id: user.uid,
        parent_id: currentBlock.id,
        name: folderData.name,
        smart_tag: 'Folder',
        banner_gradient: folderData.color,
        is_pinned: false,
        total_items: 0,
        position: subBlocks.length,
        depth: currentBlock.depth + 1
      };

      const { data, error } = await supabase
        .from('vault_blocks')
        .insert([newFolderData])
        .select()
        .single();

      if (error) {
        console.error('Error creating folder:', error);
        throw error;
      }

      setSubBlocks([...subBlocks, data]);

      // Update parent's total_items count
      await supabase
        .from('vault_blocks')
        .update({ total_items: subBlocks.length + 1 })
        .eq('id', currentBlock.id);

    } catch (error) {
      console.error('Error creating folder:', error);
      throw error;
    }
  };

  // Handle search changes from VaultSearch component
  const handleSearchChange = (query: string, filter: 'all' | 'blocks' | 'content' | 'tag', tag: string) => {
    setSearchQuery(query);
    setSearchFilter(filter);
    setSearchTag(tag);
  };

  // Handle sort changes from VaultSearch component
  const handleSortChange = (newSortBy: 'name' | 'date' | 'tag' | 'items', newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  // Create a new draft note
  const createDraftNote = async () => {
    if (!user || !blockId) return;

    try {
      const { data, error } = await supabase
        .from('block_contents')
        .insert([
          {
            block_id: blockId,
            user_id: user.uid,
            title: 'Untitled Draft',
            content: '',
            status: 'draft',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error creating draft:', error);
        return;
      }

      // Add the new draft to the state
      setDraftNotes(prev => [data, ...prev]);
      setShowDrafts(true); // Auto-expand drafts section
    } catch (error) {
      console.error('Error creating draft:', error);
    }
  };

  // Filter sub-blocks and content based on search criteria
  useEffect(() => {
    if (!searchQuery.trim() && searchFilter === 'all' && !searchTag) {
      // No search - show all items
      setFilteredSubBlocks(subBlocks);
      setFilteredBlockContents(blockContents);
      return;
    }

    let filteredBlocks: VaultBlock[] = [];
    let filteredContents: BlockContent[] = [];

    if (searchFilter === 'tag' && searchTag) {
      // Filter by tag only
      filteredBlocks = subBlocks.filter(block => block.smart_tag === searchTag);
      filteredContents = blockContents; // Show all content when filtering by tag
    } else if (searchQuery.trim()) {
      // Use fuzzy search for blocks
      if (searchFilter === 'all' || searchFilter === 'blocks') {
        const blockFuse = new Fuse(subBlocks, {
          keys: [
            { name: 'name', weight: 0.7 },
            { name: 'smart_tag', weight: 0.3 }
          ],
          threshold: 0.4,
          includeScore: true,
          minMatchCharLength: 2
        });

        const blockResults = blockFuse.search(searchQuery);
        filteredBlocks = blockResults.map(result => result.item);
      }

      // Use fuzzy search for content
      if (searchFilter === 'all' || searchFilter === 'content') {
        const contentFuse = new Fuse(blockContents, {
          keys: [
            { name: 'title', weight: 0.6 },
            { name: 'content', weight: 0.4 }
          ],
          threshold: 0.5,
          includeScore: true,
          minMatchCharLength: 2
        });

        const contentResults = contentFuse.search(searchQuery);
        filteredContents = contentResults.map(result => result.item);
      }
    } else {
      // No query but has filter - show all items
      filteredBlocks = subBlocks;
      filteredContents = blockContents;
    }

    setFilteredSubBlocks(filteredBlocks);
    setFilteredBlockContents(filteredContents);
  }, [searchQuery, searchFilter, searchTag, subBlocks, blockContents]);

  // Sort filtered items
  const sortedSubBlocks = [...filteredSubBlocks].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      case 'tag':
        comparison = a.smart_tag.localeCompare(b.smart_tag);
        break;
      case 'items':
        comparison = a.total_items - b.total_items;
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const sortedBlockContents = [...filteredBlockContents].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'date':
        comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const handleLogout = async () => {
    setLogoutLoading(true);
    try {
      await logout();
      navigate("/login");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      setLogoutLoading(false);
    }
  };

  const sidebarItems = [
    { icon: HomeIcon, label: "home", active: false, path: "/home" },
    { icon: Archive, label: vaultName.toLowerCase(), active: true, path: "/my-vault" },
    { icon: Brain, label: "mind games", active: false, path: "/mind-games" },
    { icon: Map, label: "visual maps", active: false, path: "/visual-maps" },
    { icon: Trophy, label: "achievements", active: false, path: "/achievements" },
  ];

  if (loading) {
    return <LoadingScreen message="Loading block content..." />;
  }

  if (!currentBlock) {
    return <LoadingScreen message="Block not found..." />;
  }

  return (
    <>
      {logoutLoading && <LoadingScreen message="signing you out..." />}
      <div
        className="min-h-screen w-full font-sans relative"
        style={{
          backgroundImage: 'url(/background.jpg)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        {/* Subtle overlay to tone down the background */}
        <div className="absolute inset-0 bg-[#2E0406]/40 backdrop-blur-[0.5px]" />

        {/* Top Navigation Bar */}
        <div className="w-full px-3 py-6 relative z-10">
          <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg p-4 relative z-50 max-w-[90%] mx-auto">
            <div className="flex items-center justify-between">
              {/* Left: Hamburger/Close and Logo */}
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSidebarOpen(!sidebarOpen)}
                  className="w-9 h-9 rounded-xl hover:bg-[#d4c7b8]/40 transition-all duration-200"
                >
                  {sidebarOpen ? (
                    <X className="w-4 h-4 text-[#5a4a3a]/80" />
                  ) : (
                    <Menu className="w-4 h-4 text-[#5a4a3a]/80" />
                  )}
                </Button>
                <div className="flex items-center gap-3">
                  <img
                    src="/app logo.png"
                    alt="Logo"
                    className="w-9 h-9 rounded-xl object-cover shadow-sm"
                  />
                </div>
              </div>

              {/* Center: Empty space for cleaner navbar */}
              <div className="flex-1"></div>

              {/* Right: User controls */}
              <div className="flex items-center gap-3">
                {/* User */}
                <div className="relative flex items-center gap-2" ref={userDropdownRef}>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-full hover:bg-[#d4c7b8]/40 transition-all duration-200 p-0"
                  >
                    {user?.photoURL ? (
                      <img
                        src={user.photoURL}
                        alt="Profile"
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <User className="w-5 h-5 text-[#5a4a3a]/80" />
                    )}
                  </Button>
                  <span className="text-sm font-poppins text-[#2E0406]/90">
                    {user?.displayName || 'user'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex px-3 pb-6 h-[calc(100vh-144px)] max-w-[90%] mx-auto relative z-10">
          {/* Sidebar */}
          <div className={`transition-all duration-500 ease-in-out ${
            sidebarOpen ? 'w-52 mr-5' : 'w-16 mr-4'
          }`}>
            <div className="bg-[#faf7f2]/90 backdrop-blur-md border border-[#e0d7cc]/60 rounded-2xl shadow-lg h-full relative overflow-hidden">
              {/* Full Sidebar Content */}
              <div className={`absolute inset-0 p-4 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-[-100%]'
              }`}>
                {/* Navigation Items */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm transition-all duration-200 relative ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80 backdrop-blur-sm border border-[#5a4a3a]/20 shadow-lg'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                    >
                      <item.icon className="w-4 h-4" />
                      <span>{item.label}</span>
                    </Button>
                  ))}
                </div>

                {/* Logout Button */}
                <div className="absolute bottom-4 left-4 right-4">
                  <Button
                    variant="ghost"
                    onClick={handleLogout}
                    className="w-full justify-start gap-3 h-12 rounded-xl font-poppins text-sm text-[#8b2635] hover:bg-[#8b2635]/10 hover:text-[#6d1f2c] transition-all duration-200"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              </div>

              {/* Icon-Only Sidebar Content */}
              <div className={`absolute inset-0 p-3 transition-all duration-500 ease-in-out ${
                sidebarOpen ? 'opacity-0 translate-x-[100%]' : 'opacity-100 translate-x-0'
              }`}>
                {/* Navigation Icons */}
                <div className="space-y-2 mb-8">
                  {sidebarItems.map((item, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="icon"
                      onClick={() => handleNavigation(item.path)}
                      className={`w-10 h-10 rounded-xl transition-all duration-200 ${
                        item.active
                          ? 'bg-[#2E0406]/80 text-[#faf7f2] hover:bg-[#3d1a1c]/80'
                          : 'text-[#5a4a3a] hover:bg-[#e0d7cc]/40 hover:text-[#2E0406]'
                      }`}
                      title={item.label}
                    >
                      <item.icon className="w-4 h-4" />
                    </Button>
                  ))}
                </div>

                {/* Logout Icon */}
                <div className="absolute bottom-3 left-3 right-3">
                  <Button
                    onClick={handleLogout}
                    variant="ghost"
                    size="icon"
                    className="w-10 h-10 rounded-xl text-[#8b2635] hover:bg-[#8b2635]/10 transition-all duration-200"
                    title="logout"
                  >
                    <LogOut className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 transition-all duration-500 ease-in-out">
            <div className="bg-gradient-to-br from-[#f8f4ee] via-[#faf7f2] to-[#f5f0e8] h-full overflow-hidden rounded-2xl">
              {/* Scrollable Content Container */}
              <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#8b2635] scrollbar-track-transparent hover:scrollbar-thumb-[#6d1f2c]">
                <div className="p-8">
                  {/* Block Banner Section */}
                  <div className="relative h-32 overflow-hidden rounded-3xl mb-6 shadow-2xl">
                    {currentBlock.custom_banner_image ? (
                      <img
                        src={currentBlock.custom_banner_image}
                        alt="Block Banner"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className={`w-full h-full bg-gradient-to-r ${currentBlock.banner_gradient}`} />
                    )}

                    {/* Banner Overlay */}
                    <div className="absolute inset-0 bg-black/20" />
                  </div>

                  {/* Block Title Below Banner */}
                  <div className="mb-6">
                    <div className="flex items-start justify-between mb-3">
                      <h1 className="text-4xl md:text-5xl font-cormorant font-bold text-[#2E0406]">
                        {currentBlock.name}
                      </h1>

                      {/* Create Dropdown */}
                      <div className="relative" ref={createDropdownRef}>
                        <Button
                          onClick={() => setShowCreateDropdown(!showCreateDropdown)}
                          className="bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 px-6 font-poppins shadow-lg hover:shadow-xl transition-all duration-200"
                        >
                          <Plus className="w-5 h-5 mr-2" />
                          Create
                          <ChevronDown className="w-4 h-4 ml-2" />
                        </Button>

                        {/* Dropdown Menu */}
                        {showCreateDropdown && (
                          <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-xl shadow-lg border border-[#e0d7cc]/40 py-2 z-50">
                            <button
                              onClick={() => {
                                setShowAddFolderModal(true);
                                setShowCreateDropdown(false);
                              }}
                              className="w-full px-4 py-3 text-left hover:bg-[#f5f0e8] transition-colors flex items-center gap-3 font-poppins text-[#2E0406]"
                            >
                              <FolderPlus className="w-4 h-4 text-[#8b2635]" />
                              New Folder
                            </button>
                            <button
                              onClick={() => {
                                createDraftNote();
                                setShowCreateDropdown(false);
                              }}
                              className="w-full px-4 py-3 text-left hover:bg-[#f5f0e8] transition-colors flex items-center gap-3 font-poppins text-[#2E0406]"
                            >
                              <FileText className="w-4 h-4 text-[#8b2635]" />
                              New Note
                            </button>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Block Metadata */}
                    <div className="flex items-center gap-4 text-sm text-[#8b7355] mb-3">
                      <span className="bg-[#8b2635]/10 text-[#8b2635] px-3 py-1 rounded-full font-poppins">
                        {currentBlock.smart_tag}
                      </span>
                      <span className="font-poppins">
                        {subBlocks.length} folders
                      </span>
                      <span className="font-poppins">
                        {blockContents.length} items
                      </span>
                    </div>

                    {/* Directory Path */}
                    <div className="flex items-center gap-2 text-sm">
                      <span className="text-[#8b7355] font-poppins font-medium">Directory:</span>
                      <button
                        onClick={() => navigate('/my-vault')}
                        className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                      >
                        {vaultName}
                      </button>
                      {breadcrumb.map((block, index) => (
                        <React.Fragment key={block.id}>
                          <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                          <button
                            onClick={() => navigate(`/block/${block.id}`)}
                            className="text-[#8b7355] hover:text-[#2E0406] transition-colors font-poppins"
                          >
                            {block.name}
                          </button>
                        </React.Fragment>
                      ))}
                      <ChevronRight className="w-4 h-4 text-[#8b7355]" />
                      <span className="text-[#2E0406] font-poppins font-medium">
                        {currentBlock.name}
                      </span>
                    </div>
                  </div>

                  {/* Tabbed Interface */}
                  <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'subblocks' | 'files' | 'drafts')} className="w-full">
                    <TabsList className="grid w-full grid-cols-3 mb-6 bg-[#f5f0e8] p-1 rounded-xl h-14 border border-[#e0d7cc]/40">
                      <TabsTrigger
                        value="subblocks"
                        className="bg-transparent text-[#8b7355] data-[state=active]:bg-[#8b2635] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-lg font-poppins h-full py-3 text-sm font-medium m-0 border-0 flex items-center gap-2"
                      >
                        <Folder className="w-4 h-4" />
                        Folders ({subBlocks.length})
                      </TabsTrigger>
                      <TabsTrigger
                        value="files"
                        className="bg-transparent text-[#8b7355] data-[state=active]:bg-[#8b2635] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-lg font-poppins h-full py-3 text-sm font-medium m-0 border-0 flex items-center gap-2"
                      >
                        <FileText className="w-4 h-4" />
                        Files ({blockContents.length})
                      </TabsTrigger>
                      <TabsTrigger
                        value="drafts"
                        className="bg-transparent text-[#8b7355] data-[state=active]:bg-[#8b2635] data-[state=active]:text-white data-[state=active]:shadow-none transition-all duration-300 ease-in-out rounded-lg font-poppins h-full py-3 text-sm font-medium m-0 border-0 flex items-center gap-2"
                      >
                        <Edit3 className="w-4 h-4" />
                        Drafts ({draftNotes.length})
                      </TabsTrigger>
                    </TabsList>

                    {/* Folders Tab Content */}
                    <TabsContent value="subblocks" className="mt-0">
                      {/* Search Section for Folders */}
                      <div className="mb-6">
                        <VaultSearch
                          onSearchChange={handleSearchChange}
                          onSortChange={handleSortChange}
                        />
                      </div>

                      {/* Folders Grid */}
                      {sortedSubBlocks.length > 0 ? (
                        <div className="mb-8">
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                        {sortedSubBlocks.map((subBlock) => (
                          <div
                            key={subBlock.id}
                            onClick={() => navigate(`/block/${subBlock.id}`)}
                            className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative p-4"
                          >
                            {/* Pin indicator */}
                            {subBlock.is_pinned && (
                              <div className="absolute top-2 right-2">
                                <div className="bg-[#8b2635] rounded-full p-1 shadow-md">
                                  <Pin className="w-3 h-3 text-white" />
                                </div>
                              </div>
                            )}

                            {/* Folder Icon */}
                            <div className="flex justify-center mb-3">
                              <div className="w-12 h-12 flex items-center justify-center">
                                <Folder className="w-8 h-8 text-yellow-500" />
                              </div>
                            </div>

                            {/* Folder Content */}
                            <div className="text-center">
                              {/* Folder Name and Menu */}
                              <div className="flex items-center justify-center mb-2 relative w-full">
                                {editingSubBlockId === subBlock.id ? (
                                  <input
                                    type="text"
                                    value={editingSubBlockName}
                                    onChange={(e) => setEditingSubBlockName(e.target.value)}
                                    onBlur={() => handleSaveSubBlockName(subBlock.id)}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        handleSaveSubBlockName(subBlock.id);
                                      } else if (e.key === 'Escape') {
                                        handleCancelEdit();
                                      }
                                    }}
                                    className="w-full text-center font-cormorant font-bold text-[#2E0406] text-sm bg-white/80 border border-[#8b2635] rounded-lg px-2 py-1 focus:outline-none focus:ring-2 focus:ring-[#8b2635]/50"
                                    autoFocus
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                ) : (
                                  <h3 className="font-cormorant font-bold text-[#2E0406] text-sm group-hover:text-[#8b2635] transition-colors text-center px-1 truncate">
                                    {subBlock.name}
                                  </h3>
                                )}

                                {/* Ellipsis Menu */}
                                <div className="absolute top-0 right-0">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setOpenSubBlockMenuId(openSubBlockMenuId === subBlock.id ? null : subBlock.id);
                                    }}
                                    className="w-6 h-6 rounded-lg hover:bg-[#8b2635]/10 flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100"
                                  >
                                    <MoreVertical className="w-4 h-4 text-[#8b2635]" />
                                  </button>

                                  {/* Dropdown Menu */}
                                  {openSubBlockMenuId === subBlock.id && (
                                    <div
                                      className="absolute right-0 top-8 w-44 bg-white/90 backdrop-blur-md border border-white/30 rounded-xl shadow-2xl p-2 z-[9999]"
                                      onClick={(e) => e.stopPropagation()}
                                    >
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleEditSubBlock(subBlock);
                                        }}
                                        className="w-full text-left px-3 py-2 text-[#2E0406] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors flex items-center gap-2"
                                      >
                                        <Edit3 className="w-4 h-4" />
                                        Edit Name
                                      </button>
                                      <button
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleDeleteSubBlock(subBlock.id);
                                        }}
                                        className="w-full text-left px-3 py-2 text-[#8b2635] font-poppins text-sm hover:bg-[#8b2635]/10 rounded-lg transition-colors flex items-center gap-2"
                                      >
                                        <Trash2 className="w-4 h-4" />
                                        Delete Folder
                                      </button>
                                    </div>
                                  )}
                                </div>
                              </div>

                              <div className="text-xs text-[#6d1f2c] font-poppins">
                                {formatDate(subBlock.created_at)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                        </div>
                      ) : (
                        <div className="text-center py-16">
                          <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                            <Folder className="w-12 h-12 text-[#8b7355]" />
                          </div>
                          <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No folders yet</h3>
                          <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                            Create folders to organize your knowledge into structured sections.
                          </p>
                        </div>
                      )}
                    </TabsContent>

                    {/* Files Tab Content */}
                    <TabsContent value="files" className="mt-0">
                      {/* Search Section for Files */}
                      <div className="mb-6">
                        <VaultSearch
                          onSearchChange={handleSearchChange}
                          onSortChange={handleSortChange}
                        />
                      </div>

                      {/* File Drop Zone */}
                      <div
                        onDragOver={handleDragOver}
                        onDragLeave={handleDragLeave}
                        onDrop={handleDrop}
                        className={`relative ${isDragOver ? 'bg-blue-50 border-blue-300' : ''}`}
                      >
                        {isDragOver && (
                          <div className="absolute inset-0 bg-blue-100/80 border-2 border-dashed border-blue-400 rounded-xl flex items-center justify-center z-10">
                            <div className="text-center">
                              <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <FileText className="w-8 h-8 text-white" />
                              </div>
                              <p className="text-blue-700 font-poppins font-semibold text-lg">Drop files here</p>
                              <p className="text-blue-600 font-poppins text-sm">Files will be uploaded to this section</p>
                            </div>
                          </div>
                        )}

                        {/* Files Grid */}
                        {sortedBlockContents.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                          {sortedBlockContents.map((content) => {
                            const fileTypeInfo = getFileTypeIcon(content);
                            const IconComponent = fileTypeInfo.icon;

                            return (
                              <div
                                key={content.id}
                                className="group bg-white/60 backdrop-blur-sm border border-[#e0d7cc]/40 rounded-2xl hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer relative p-4"
                              >
                                {/* File Icon */}
                                <div className="flex justify-center mb-4">
                                  <div className={`w-16 h-16 ${fileTypeInfo.bgColor} rounded-xl flex items-center justify-center`}>
                                    <IconComponent className={`w-8 h-8 ${fileTypeInfo.color}`} />
                                  </div>
                                </div>

                                {/* File Content */}
                                <div className="text-center">
                                  {/* File Name and Menu */}
                                  <div className="flex items-center justify-center mb-2 relative">
                                    <h3 className="font-cormorant font-bold text-[#2E0406] text-lg group-hover:text-[#8b2635] transition-colors truncate">
                                      {content.title || 'Untitled'}
                                    </h3>

                                    {/* Ellipsis Menu */}
                                    <div className="absolute top-0 right-0">
                                      <button className="w-6 h-6 rounded-lg hover:bg-[#8b2635]/10 flex items-center justify-center transition-all duration-200 opacity-0 group-hover:opacity-100">
                                        <MoreVertical className="w-4 h-4 text-[#8b2635]" />
                                      </button>
                                    </div>
                                  </div>

                                  <div className="text-sm text-[#6d1f2c] font-poppins">
                                    {formatDate(content.created_at)}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      ) : (
                        <div className="text-center py-16">
                          <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                            <FileText className="w-12 h-12 text-[#8b7355]" />
                          </div>
                          <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No files yet</h3>
                          <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                            Upload or drop files here to get started.
                          </p>
                        </div>
                      )}
                      </div>
                    </TabsContent>

                    {/* Drafts Tab Content */}
                    <TabsContent value="drafts" className="mt-0">
                      {/* Drafts Grid */}
                      {draftNotes.length > 0 ? (
                        <div className="space-y-4">
                          {draftNotes.map((draft) => (
                            <div
                              key={draft.id}
                              className="bg-white/40 border border-dashed border-[#e0d7cc] rounded-xl p-4 hover:bg-white/60 transition-colors"
                            >
                              <div className="flex items-start justify-between mb-2">
                                <h4 className="font-cormorant font-semibold text-[#2E0406] text-lg">
                                  {draft.title || 'Untitled Draft'}
                                </h4>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded-full font-poppins">
                                    Draft
                                  </span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 hover:bg-[#e0d7cc]/40"
                                  >
                                    <Edit3 className="w-3 h-3 text-[#8b7355]" />
                                  </Button>
                                </div>
                              </div>
                              <p className="text-[#6d1f2c] font-poppins text-sm opacity-75 line-clamp-2">
                                {draft.content || 'No content yet...'}
                              </p>
                              <div className="flex items-center justify-between mt-3 pt-3 border-t border-dashed border-[#e0d7cc]/50">
                                <span className="text-xs text-[#8b7355] font-poppins">
                                  Last edited: {formatDate(draft.updated_at || draft.created_at)}
                                </span>
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 px-3 text-xs font-poppins text-[#8b2635] hover:bg-[#8b2635]/10"
                                  >
                                    Continue Writing
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-7 px-3 text-xs font-poppins text-green-600 hover:bg-green-50"
                                  >
                                    Publish
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-16">
                          <div className="w-24 h-24 bg-[#e0d7cc]/40 rounded-3xl flex items-center justify-center mx-auto mb-6">
                            <Edit3 className="w-12 h-12 text-[#8b7355]" />
                          </div>
                          <h3 className="font-cormorant font-bold text-[#2E0406] text-2xl mb-3">No drafts yet</h3>
                          <p className="text-[#8b7355] font-poppins mb-6 max-w-md mx-auto">
                            Start writing notes and save them as drafts before publishing.
                          </p>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Add Sub-Block Modal */}
        {showAddBlockModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-md mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Folder</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddBlockModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Block Name
                  </label>
                  <input
                    type="text"
                    value={newBlockName}
                    onChange={(e) => setNewBlockName(e.target.value)}
                    placeholder="Enter block name..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                    autoFocus
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddBlockModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={createSubBlock}
                    disabled={!newBlockName.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create Block
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Content Modal */}
        {showAddContentModal && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999]">
            <div className="bg-white/90 backdrop-blur-md border border-white/30 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-cormorant font-bold text-[#2E0406]">Add Content</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowAddContentModal(false)}
                  className="w-8 h-8 rounded-full hover:bg-[#8b2635]/10 transition-all duration-200"
                >
                  <X className="w-4 h-4 text-[#8b2635]" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content Title
                  </label>
                  <input
                    type="text"
                    value={newContentTitle}
                    onChange={(e) => setNewContentTitle(e.target.value)}
                    placeholder="Enter content title..."
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-poppins font-medium text-[#2E0406] mb-2">
                    Content
                  </label>
                  <textarea
                    value={newContentText}
                    onChange={(e) => setNewContentText(e.target.value)}
                    placeholder="Enter your content..."
                    rows={6}
                    className="w-full px-4 py-3 bg-white/60 border border-[#e0d7cc]/40 rounded-xl text-[#2E0406] placeholder-[#8b7355] font-poppins focus:outline-none focus:ring-2 focus:ring-[#8b2635]/30 focus:border-[#8b2635] transition-all duration-200 resize-none"
                  />
                </div>
                
                <div className="flex gap-3 pt-4">
                  <Button
                    onClick={() => setShowAddContentModal(false)}
                    variant="outline"
                    className="flex-1 border-[#e0d7cc] text-[#8b7355] hover:bg-[#e0d7cc]/20 rounded-xl h-12 font-poppins"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => {
                      // TODO: Implement content creation
                      setShowAddContentModal(false);
                      setNewContentTitle("");
                      setNewContentText("");
                    }}
                    disabled={!newContentTitle.trim() || !newContentText.trim()}
                    className="flex-1 bg-gradient-to-r from-[#8b2635] to-[#6d1f2c] hover:from-[#6d1f2c] hover:to-[#8b2635] text-white border-0 rounded-xl h-12 font-poppins disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Content
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add Folder Modal */}
        <AddFolderModal
          isOpen={showAddFolderModal}
          onClose={() => setShowAddFolderModal(false)}
          onCreateFolder={createFolder}
        />
      </div>
    </>
  );
};
